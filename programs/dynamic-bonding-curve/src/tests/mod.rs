#[cfg(test)]
mod price_math;

#[cfg(test)]
mod test_create_config;

#[cfg(test)]
mod test_swap;

#[cfg(test)]
mod test_volitility_accumulate;

#[cfg(test)]
mod test_total_supply;

#[cfg(test)]
mod test_migration_fee_status;

#[cfg(test)]
mod test_rate_limiter;

#[cfg(test)]
mod test_dynamic_fee_params;

#[cfg(test)]
mod test_inverse_fee;

#[cfg(test)]
mod test_math_utils;

#[cfg(test)]
mod test_donation_attack_vulnerability;

#[cfg(test)]
mod test_freeze_authority_vulnerability;

#[cfg(test)]
mod test_token2022_freeze_authority_realistic_poc;

#[cfg(test)]
mod test_metadata_pda_substitution_vulnerability;

#[cfg(test)]
mod test_metadata_pda_substitution_exploit;

#[cfg(test)]
mod test_real_exploit_attempt;

#[cfg(test)]
mod test_permissionless_pool_creation;
mod permissionless_pool_creation_poc;

#[cfg(test)]
mod arbitrary_cpi_initialize_pool_vulnerability_poc;

#[cfg(test)]
mod transfer_fee_extension_vulnerability_poc;
