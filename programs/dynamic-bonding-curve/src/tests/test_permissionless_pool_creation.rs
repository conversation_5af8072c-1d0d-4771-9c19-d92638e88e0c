use anchor_lang::prelude::*;
use crate::{
    constants::seeds::POOL_PREFIX,
    state::{PoolConfig, VirtualPool},
};

// POC: Permissionless pool creation under a public PoolConfig.
// Assertions-only; no prints. Mirrors the root tests/ POC but lives inside the crate
// so it runs with `cargo test -p dynamic-bonding-curve`.

#[cfg(test)]
mod tests {
    use super::*;
    use std::cmp::{max, min};

    #[test]
    fn pool_pda_does_not_depend_on_creator() {
        let config = Pubkey::new_unique();
        let base_mint = Pubkey::new_unique();
        let quote_mint = Pubkey::new_unique();

        let larger = max(base_mint, quote_mint).to_bytes();
        let smaller = min(base_mint, quote_mint).to_bytes();

        let (pda_1, _) = Pubkey::find_program_address(
            &[POOL_PREFIX, config.as_ref(), &larger, &smaller],
            &crate::id(),
        );

        // Different would-be creator cannot affect PDA since not in seeds
        let _other_creator = Pubkey::new_unique();
        let (pda_2, _) = Pubkey::find_program_address(
            &[POOL_PREFIX, config.as_ref(), &larger, &smaller],
            &crate::id(),
        );

        assert_eq!(pda_1, pda_2);
    }

    #[test]
    fn initialize_sets_creator_to_caller() {
        let creator_a = Pubkey::new_unique();
        let creator_b = Pubkey::new_unique();

        let config = Pubkey::new_unique();
        let base_mint = Pubkey::new_unique();
        let base_vault = Pubkey::new_unique();
        let quote_vault = Pubkey::new_unique();
        let sqrt_price: u128 = 1u128 << 64;
        let pool_type: u8 = 0; // SplToken
        let activation_point: u64 = 0;
        let base_reserve: u64 = 1;

        let mut pool_a: VirtualPool = Default::default();
        pool_a.initialize(
            Default::default(),
            config,
            creator_a,
            base_mint,
            base_vault,
            quote_vault,
            sqrt_price,
            pool_type,
            activation_point,
            base_reserve,
        );
        assert_eq!(pool_a.creator, creator_a);

        let mut pool_b: VirtualPool = Default::default();
        pool_b.initialize(
            Default::default(),
            config,
            creator_b,
            base_mint,
            base_vault,
            quote_vault,
            sqrt_price,
            pool_type,
            activation_point,
            base_reserve,
        );
        assert_eq!(pool_b.creator, creator_b);
    }

    #[test]
    fn poolconfig_lacks_admin_gating_field() {
        let cfg: PoolConfig = Default::default();
        let _ = cfg.quote_mint;
        let _ = cfg.fee_claimer;
        let _ = cfg.token_type;
        assert!(true);
    }
}

